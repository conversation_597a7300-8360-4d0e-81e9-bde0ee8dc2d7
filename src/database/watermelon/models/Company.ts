import { Model } from '@nozbe/watermelondb';
import { field, date } from '@nozbe/watermelondb/decorators';

export default class Company extends Model {
  static table = 'companies';

  @field('company_id') companyId!: number; // Store original API companyId
  @field('company_name') companyName!: string;
  @field('parent_company') parentCompany!: string;
  @field('company_email') companyEmail!: string;
  @field('company_logo_url') companyLogoUrl!: string;
  @field('company_country') companyCountry!: string;
  @field('company_address') companyAddress!: string;
  @field('company_website') companyWebsite!: string;
  @field('number') number!: string; // Added number field
  @field('is_whatsapp') isWhatsapp!: number; // 0 or 1 // Added field to track number
  @field('upvote_count') upvoteCount!: number;
  @field('downvote_count') downvoteCount!: number;
  @date('created_at') createdAt!: Date;
  @date('updated_at') updatedAt!: Date;
}
