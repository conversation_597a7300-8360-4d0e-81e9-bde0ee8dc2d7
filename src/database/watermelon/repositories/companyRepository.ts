import { Q } from '@nozbe/watermelondb';
import database from '../database';
import Company from '../models/Company';

export interface CompanyData {
  id?: string; // WatermelonDB ID (string)
  company_id?: number; // Original API companyId (number)
  company_name: string;
  parent_company?: string;
  company_email?: string;
  company_logo_url?: string;
  company_country?: string;
  company_address?: string;
  company_website?: string;
  number?: string; // Added number field
  is_whatsapp?: number; // 0 or 1, Added field to track number
  company_priority?: number; // Priority for sorting (lower values appear first)
  upvote_count?: number;
  downvote_count?: number;
  created_at?: Date;
  updated_at?: Date;
}

class WatermelonCompanyRepository {
  private collection = database.get<Company>('companies');

  async getAll(): Promise<CompanyData[]> {
    try {
      const companies = await this.collection.query().fetch();
      return companies.map(company => ({
        id: company.id,
        company_id: company.companyId,
        company_name: company.companyName,
        parent_company: company.parentCompany,
        company_email: company.companyEmail,
        company_logo_url: company.companyLogoUrl,
        company_country: company.companyCountry,
        company_address: company.companyAddress,
        company_website: company.companyWebsite,
        number: company.number,
        is_whatsapp: company.isWhatsapp,
        company_priority: company.companyPriority,
        upvote_count: company.upvoteCount,
        downvote_count: company.downvoteCount,
        created_at: company.createdAt,
        updated_at: company.updatedAt,
      }));
    } catch (error) {
      console.error('Error getting companies:', error);
      throw error;
    }
  }

  async getById(id: string): Promise<CompanyData | null> {
    try {
      const company = await this.collection.find(id);
      return {
        id: company.id,
        company_id: company.companyId,
        company_name: company.companyName,
        parent_company: company.parentCompany,
        company_email: company.companyEmail,
        company_logo_url: company.companyLogoUrl,
        company_country: company.companyCountry,
        company_address: company.companyAddress,
        company_website: company.companyWebsite,
        number: company.number,
        is_whatsapp: company.isWhatsapp,
        company_priority: company.companyPriority,
        upvote_count: company.upvoteCount,
        downvote_count: company.downvoteCount,
        created_at: company.createdAt,
        updated_at: company.updatedAt,
      };
    } catch (error) {
      console.error('Error getting company by id:', error);
      return null;
    }
  }

  async getByCompanyId(companyId: number): Promise<CompanyData | null> {
    try {
      const companies = await this.collection
        .query(Q.where('company_id', companyId))
        .fetch();

      if (companies.length === 0) {
        return null;
      }

      const company = companies[0];
      return {
        id: company.id,
        company_id: company.companyId,
        company_name: company.companyName,
        parent_company: company.parentCompany,
        company_email: company.companyEmail,
        company_logo_url: company.companyLogoUrl,
        company_country: company.companyCountry,
        company_address: company.companyAddress,
        company_website: company.companyWebsite,
        number: company.number,
        is_whatsapp: company.isWhatsapp,
        company_priority: company.companyPriority,
        upvote_count: company.upvoteCount,
        downvote_count: company.downvoteCount,
        created_at: company.createdAt,
        updated_at: company.updatedAt,
      };
    } catch (error) {
      console.error('Error getting company by company ID:', error);
      return null;
    }
  }

  // Batch method to fetch multiple companies by their IDs at once
  async getByCompanyIds(companyIds: number[]): Promise<CompanyData[]> {
    try {
      if (companyIds.length === 0) {
        return [];
      }

      console.log(`[CompanyRepository] Fetching ${companyIds.length} companies in batch`);
      const startTime = Date.now();

      const companies = await this.collection
        .query(Q.where('company_id', Q.oneOf(companyIds)))
        .fetch();

      const endTime = Date.now();
      console.log(`[CompanyRepository] Batch fetch completed in ${endTime - startTime}ms`);

      return companies.map(company => ({
        id: company.id,
        company_id: company.companyId,
        company_name: company.companyName,
        parent_company: company.parentCompany,
        company_email: company.companyEmail,
        company_logo_url: company.companyLogoUrl,
        company_country: company.companyCountry,
        company_address: company.companyAddress,
        company_website: company.companyWebsite,
        number: company.number,
        is_whatsapp: company.isWhatsapp,
        company_priority: company.companyPriority,
        upvote_count: company.upvoteCount,
        downvote_count: company.downvoteCount,
        created_at: company.createdAt,
        updated_at: company.updatedAt,
      }));
    } catch (error) {
      console.error('Error getting companies by ids:', error);
      throw error;
    }
  }

  // Get companies sorted by priority (lower values first) and then by name
  async getAllSortedByPriority(): Promise<CompanyData[]> {
    try {
      const companies = await this.collection
        .query(Q.sortBy('company_priority', Q.asc), Q.sortBy('company_name', Q.asc))
        .fetch();

      return companies.map(company => ({
        id: company.id,
        company_id: company.companyId,
        company_name: company.companyName,
        parent_company: company.parentCompany,
        company_email: company.companyEmail,
        company_logo_url: company.companyLogoUrl,
        company_country: company.companyCountry,
        company_address: company.companyAddress,
        company_website: company.companyWebsite,
        number: company.number,
        is_whatsapp: company.isWhatsapp,
        company_priority: company.companyPriority,
        upvote_count: company.upvoteCount,
        downvote_count: company.downvoteCount,
        created_at: company.createdAt,
        updated_at: company.updatedAt,
      }));
    } catch (error) {
      console.error('Error getting companies sorted by priority:', error);
      throw error;
    }
  }

  async create(companyData: CompanyData): Promise<string> {
    try {
      const newCompany = await database.write(async () => {
        return await this.collection.create(company => {
          company.companyId = companyData.company_id || 0;
          company.companyName = companyData.company_name;
          company.parentCompany = companyData.parent_company || '';
          company.companyEmail = companyData.company_email || '';
          company.companyLogoUrl = companyData.company_logo_url || '';
          company.companyCountry = companyData.company_country || '';
          company.companyAddress = companyData.company_address || '';
          company.companyWebsite = companyData.company_website || '';
          company.number = companyData.number || ''; // Added number field
          company.isWhatsapp = companyData.is_whatsapp || 0;
          company.companyPriority = companyData.company_priority || 999999; // Default to high value if not provided
          company.upvoteCount = companyData.upvote_count || 0;
          company.downvoteCount = companyData.downvote_count || 0;
        });
      });
      return newCompany.id;
    } catch (error) {
      console.error('Error creating company:', error);
      throw error;
    }
  }

  async update(companyData: CompanyData): Promise<void> {
    if (!companyData.id) {
      throw new Error('Company ID is required for update');
    }

    try {
      await database.write(async () => {
        const company = await this.collection.find(companyData.id!);
        await company.update(company => {
          company.companyId = companyData.company_id || 0;
          company.companyName = companyData.company_name;
          company.parentCompany = companyData.parent_company || '';
          company.companyEmail = companyData.company_email || '';
          company.companyLogoUrl = companyData.company_logo_url || '';
          company.companyCountry = companyData.company_country || '';
          company.companyAddress = companyData.company_address || '';
          company.companyWebsite = companyData.company_website || '';
          company.number = companyData.number || '';
          company.isWhatsapp = companyData.is_whatsapp || 0;
          company.companyPriority = companyData.company_priority || 999999; // Default to high value if not provided
          company.upvoteCount = companyData.upvote_count || 0;
          company.downvoteCount = companyData.downvote_count || 0;
        });
      });
    } catch (error) {
      console.error('Error updating company:', error);
      throw error;
    }
  }

  async delete(id: string): Promise<void> {
    try {
      await database.write(async () => {
        const company = await this.collection.find(id);
        await company.destroyPermanently();
      });
    } catch (error) {
      console.error('Error deleting company:', error);
      throw error;
    }
  }

  async createOrUpdate(companyData: CompanyData): Promise<string> {
    if (companyData.id) {
      const existing = await this.getById(companyData.id);
      if (existing) {
        await this.update(companyData);
        return companyData.id;
      }
    }
    return await this.create(companyData);
  }

  async createOrUpdateByCompanyId(companyData: CompanyData): Promise<string> {
    if (companyData.company_id) {
      const existing = await this.getByCompanyId(companyData.company_id);
      if (existing) {
        await this.update({ ...companyData, id: existing.id });
        return existing.id;
      }
    }
    return await this.create(companyData);
  }

  async batchCreate(companiesData: CompanyData[]): Promise<void> {
    try {
      console.log(`[CompanyRepository] Starting batch create for ${companiesData.length} companies`);

      // Process in smaller chunks if the batch is too large
      const maxBatchSize = 500; // WatermelonDB batch size limit

      if (companiesData.length <= maxBatchSize) {
        // Single batch
        await database.write(async () => {
          const batch = companiesData.map(companyData =>
            this.collection.prepareCreate(company => {
              company.companyId = companyData.company_id || 0;
              company.companyName = companyData.company_name;
              company.parentCompany = companyData.parent_company || '';
              company.companyEmail = companyData.company_email || '';
              company.companyLogoUrl = companyData.company_logo_url || '';
              company.companyCountry = companyData.company_country || '';
              company.companyAddress = companyData.company_address || '';
              company.companyWebsite = companyData.company_website || '';
              company.number = companyData.number || ''; // Added number field
              company.isWhatsapp = companyData.is_whatsapp || 0;
              company.upvoteCount = companyData.upvote_count || 0;
              company.downvoteCount = companyData.downvote_count || 0;
            })
          );
          await database.batch(batch);
        });
      } else {
        // Multiple batches for large datasets
        for (let i = 0; i < companiesData.length; i += maxBatchSize) {
          const chunk = companiesData.slice(i, i + maxBatchSize);
          await database.write(async () => {
            const batch = chunk.map(companyData =>
              this.collection.prepareCreate(company => {
                company.companyId = companyData.company_id || 0;
                company.companyName = companyData.company_name;
                company.parentCompany = companyData.parent_company || '';
                company.companyEmail = companyData.company_email || '';
                company.companyLogoUrl = companyData.company_logo_url || '';
                company.companyCountry = companyData.company_country || '';
                company.companyAddress = companyData.company_address || '';
                company.companyWebsite = companyData.company_website || '';
                company.number = companyData.number || '';
                company.isWhatsapp = companyData.is_whatsapp || 0;
                company.upvoteCount = companyData.upvote_count || 0;
                company.downvoteCount = companyData.downvote_count || 0;
              })
            );
            await database.batch(batch);
          });
        }
      }

      console.log(`Batch created ${companiesData.length} companies`);
    } catch (error) {
      console.error('Error batch creating companies:', error);
      throw error;
    }
  }

  async batchCreateOrUpdate(companiesData: CompanyData[]): Promise<void> {
    try {
      console.log(`[CompanyRepository] Starting batch create or update for ${companiesData.length} companies`);

      // Get all existing companies by API company_id for efficient lookup
      const existingCompanies = await this.collection.query().fetch();
      const existingByCompanyId = new Map<number, string>(); // Map API company_id to WatermelonDB id

      existingCompanies.forEach(company => {
        existingByCompanyId.set(company.companyId, company.id);
      });

      console.log(`[CompanyRepository] Found ${existingByCompanyId.size} existing companies`);

      // Separate new and existing companies
      const toCreate: CompanyData[] = [];
      const toUpdate: CompanyData[] = [];

      companiesData.forEach(companyData => {
        if (companyData.company_id && existingByCompanyId.has(companyData.company_id)) {
          // Company exists, add to update list with WatermelonDB id
          toUpdate.push({
            ...companyData,
            id: existingByCompanyId.get(companyData.company_id),
          });
        } else {
          // New company, add to create list
          toCreate.push(companyData);
        }
      });

      console.log(`[CompanyRepository] ${toCreate.length} companies to create, ${toUpdate.length} companies to update`);

      // Process updates in batches
      if (toUpdate.length > 0) {
        const maxBatchSize = 250;
        for (let i = 0; i < toUpdate.length; i += maxBatchSize) {
          const chunk = toUpdate.slice(i, i + maxBatchSize);

          // Fetch all records we need to update outside the write transaction
          const recordsToUpdate = [];
          for (const companyData of chunk) {
            try {
              const existingCompany = await this.collection.find(companyData.id!);
              recordsToUpdate.push({ record: existingCompany, data: companyData });
            } catch (findError) {
              console.error(`[CompanyRepository] Error finding company ${companyData.id} for update:`, findError);
              // Skip this update if company not found
            }
          }

          // Prepare updates and batch them
          if (recordsToUpdate.length > 0) {
            await database.write(async () => {
              const batch = recordsToUpdate.map(({ record, data }) =>
                record.prepareUpdate(company => {
                  company.companyId = data.company_id || 0;
                  company.companyName = data.company_name;
                  company.parentCompany = data.parent_company || '';
                  company.companyEmail = data.company_email || '';
                  company.companyLogoUrl = data.company_logo_url || '';
                  company.companyCountry = data.company_country || '';
                  company.companyAddress = data.company_address || '';
                  company.companyWebsite = data.company_website || '';
                  company.number = data.number || '';
                  company.isWhatsapp = data.is_whatsapp || 0;
                  company.companyPriority = data.company_priority || 999999; // Default to high value if not provided
                  company.upvoteCount = data.upvote_count || 0;
                  company.downvoteCount = data.downvote_count || 0;
                })
              );
              await database.batch(batch);
            });
          }

          console.log(`[CompanyRepository] Updated ${recordsToUpdate.length}/${chunk.length} companies in chunk ${Math.floor(i / maxBatchSize) + 1}`);
        }
      }

      // Process creates in batches
      if (toCreate.length > 0) {
        await this.batchCreate(toCreate);
      }

      console.log(`[CompanyRepository] ✅ Batch create or update completed: ${toCreate.length} created, ${toUpdate.length} updated`);
    } catch (error) {
      console.error('Error batch creating or updating companies:', error);
      throw error;
    }
  }

  async getCount(): Promise<number> {
    try {
      const count = await this.collection.query().fetchCount();
      return count;
    } catch (error) {
      console.error('Error getting companies count:', error);
      throw error;
    }
  }

  async clearAll(): Promise<void> {
    try {
      await database.write(async () => {
        const allCompanies = await this.collection.query().fetch();
        const batch = allCompanies.map(company => company.prepareDestroyPermanently());
        await database.batch(batch);
      });
      console.log('Cleared all companies from database');
    } catch (error) {
      console.error('Error clearing companies:', error);
      throw error;
    }
  }

  // Debug method to check number field storage
  async checkNumberFields(): Promise<void> {
    try {
      const companies = await this.collection.query().fetch();
      console.log(`[CompanyRepository] Checking number fields for ${companies.length} companies`);

      let companiesWithNumbers = 0;
      let companiesWithoutNumbers = 0;

      companies.slice(0, 10).forEach((company, index) => {
        console.log(`[CompanyRepository] Company ${index + 1}: ${company.companyName} - Number: "${company.number}"`);
        if (company.number && company.number.trim() !== '') {
          companiesWithNumbers++;
        } else {
          companiesWithoutNumbers++;
        }
      });

      console.log(`[CompanyRepository] Summary: ${companiesWithNumbers} with numbers, ${companiesWithoutNumbers} without numbers (from first 10)`);
    } catch (error) {
      console.error('[CompanyRepository] Error checking number fields:', error);
    }
  }
}

export default new WatermelonCompanyRepository();
