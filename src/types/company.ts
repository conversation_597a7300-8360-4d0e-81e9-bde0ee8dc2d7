/**
 * Shared company interfaces to ensure consistency across the application
 */

import Number from "../database/watermelon/models/Number";

// Base company interface that matches the API response structure
export interface BaseCompany {
  companyId: number;
  companyName: string;
  parentCompany?: string | null;
  companyEmail?: string | null;
  companyLogoUrl?: string | null;
  companyCountry?: string | null;
  companyAddress?: string | null;
  companyWebsite?: string | null;
  upVoteCount?: number;
  downVoteCount?: number;
  createdAt?: string;
}

// Number structure from the new API format
export interface ApiNumber {
  numberId: number;
  companyId: number;
  number: string;
  description: string;
  type: string; // TOLL_FREE, ALL_INDIA, INTERNATIONAL
  upvoteCount: number;
  downvoteCount: number;
  isWhatsapp: boolean;
}

// Company interface for API responses (both /company and /company/get-all)
export interface ApiCompany extends BaseCompany {
  number?: string | {
    TOLL_FREE?: ApiNumber[];
    ALL_INDIA?: ApiNumber[];
    INTERNATIONAL?: ApiNumber[];
  } | null;
  categories?: Array<{
    categoryId: number;
    name: string;
    iconUrl: string;
    isActive: boolean;
  }>;
}

// Company interface for UI components (cards, lists, etc.)
export interface UICompany extends BaseCompany {
  number: string; // Always a string for UI display (first available number)
  // Additional UI-specific fields
  lat?: string | number | null;
  long?: string | number | null;
  fromHistory?: boolean;
  isWhatsapp?: number;
}

// Company interface for local database storage
export interface DBCompany {
  id?: string; // WatermelonDB ID (string)
  company_id?: number; // Original API companyId (number)
  company_name: string;
  parent_company?: string;
  company_email?: string;
  company_logo_url?: string;
  company_country?: string;
  company_address?: string;
  company_website?: string;
  number?: string; // First available number stored as string
  is_whatsapp?: number; // First available number is whatsapp or not
  upvote_count?: number;
  downvote_count?: number;
  created_at?: Date;
  updated_at?: Date;
}

// Number interface for local database storage
export interface DBNumber {
  id?: string; // WatermelonDB ID (string)
  api_number_id?: number; // Original API numberId (number)
  company_id: number; // API companyId
  number: string;
  description?: string;
  type: string; // TOLL_FREE, ALL_INDIA, INTERNATIONAL
  upvote_count?: number;
  downvote_count?: number;
  is_whatsapp?: number; // 0 or 1
  created_at?: Date;
  updated_at?: Date;
}

// Utility type for converting between different company formats
export type CompanyConverter = {
  // Convert API company to UI company
  apiToUI: (apiCompany: ApiCompany) => UICompany;
  // Convert DB company to UI company
  dbToUI: (dbCompany: DBCompany) => UICompany;
  // Convert API company to DB company
  apiToDB: (apiCompany: ApiCompany) => DBCompany;
};

// API Response interfaces
export interface CompanyListResponse {
  message: string;
  data: {
    total: number;
    page: number;
    limit: number;
    companies: ApiCompany[];
  };
  success: boolean;
}

export interface CompanyDetailsResponse {
  data: ApiCompany & {
    contactNumbers?: {
      TOLL_FREE: ApiNumber[];
      ALL_INDIA: ApiNumber[];
      INTERNATIONAL: ApiNumber[];
    };
  };
  success: boolean;
  message?: string;
}

// Export legacy aliases for backward compatibility
export type Company = UICompany; // For UI components
export type CompanyData = UICompany; // For UI components
export type NumberData = DBNumber; // For database operations
