/**
 * Utility functions to convert between different company data formats
 */

import { ApiCompany, UICompany, DBCompany, CompanyConverter } from '../types/company';
import { extractFirstAvailableNumber } from './numberUtils';

export const companyConverter: CompanyConverter = {
  
  /**
   * Convert API company response to UI company format
   */
  apiToUI: (apiCompany: ApiCompany): UICompany => {
    // Extract first available number using priority logic
    const firstNumberData = extractFirstAvailableNumber(apiCompany.number) || '';
    let firstNumber = ""
    let firstWhatsapp = 0

    if (firstNumberData) {
        const { number, isWhatsapp } = firstNumberData;
        console.log('Number:', number);
        console.log('Is WhatsApp:', isWhatsapp);
        firstNumber = number;
        firstWhatsapp = isWhatsapp ? 1 : 0;
    }
    return {
      companyId: apiCompany.companyId,
      companyName: apiCompany.companyName,
      parentCompany: apiCompany.parentCompany || null,
      companyEmail: apiCompany.companyEmail || null,
      companyLogoUrl: apiCompany.companyLogoUrl || '',
      companyCountry: apiCompany.companyCountry || null,
      companyAddress: apiCompany.companyAddress || null,
      companyWebsite: apiCompany.companyWebsite || null,
      upVoteCount: apiCompany.upVoteCount || 0,
      downVoteCount: apiCompany.downVoteCount || 0,
      createdAt: apiCompany.createdAt || '',
      number: firstNumber || '',
      isWhatsapp: firstWhatsapp || 0,
    };
  },

  /**
   * Convert database company to UI company format
   */
  dbToUI: (dbCompany: DBCompany): UICompany => {
    return {
      companyId: dbCompany.company_id || 0,
      companyName: dbCompany.company_name || '',
      parentCompany: dbCompany.parent_company || null,
      companyEmail: dbCompany.company_email || null,
      companyLogoUrl: dbCompany.company_logo_url || '',
      companyCountry: dbCompany.company_country || null,
      companyAddress: dbCompany.company_address || null,
      companyWebsite: dbCompany.company_website || null,
      upVoteCount: dbCompany.upvote_count || 0,
      downVoteCount: dbCompany.downvote_count || 0,
      createdAt: dbCompany.created_at ? dbCompany.created_at.toISOString() : '',
      number: dbCompany.number || '',
      isWhatsapp: dbCompany.is_whatsapp || 0,
    };
  },

  /**
   * Convert API company to database company format
   */
  apiToDB: (apiCompany: ApiCompany): DBCompany => {
    // Extract first available number for storage in companies table
    const firstNumberData = extractFirstAvailableNumber(apiCompany.number) || '';
    
    let firstNumber = ""
    let firstWhatsapp = 0

    if (firstNumberData) {
        const { number, isWhatsapp } = firstNumberData;
        console.log('Number:', number);
        console.log('Is WhatsApp:', isWhatsapp);
        firstNumber = number;
        firstWhatsapp = isWhatsapp ? 1 : 0;
    }

    return {
      company_id: apiCompany.companyId,
      company_name: apiCompany.companyName,
      parent_company: apiCompany.parentCompany || '',
      company_email: apiCompany.companyEmail || '',
      company_logo_url: apiCompany.companyLogoUrl || '',
      company_country: apiCompany.companyCountry || '',
      company_address: apiCompany.companyAddress || '',
      company_website: apiCompany.companyWebsite || '',
      number: firstNumber,
      is_whatsapp: firstWhatsapp,
      upvote_count: apiCompany.upVoteCount || 0,
      downvote_count: apiCompany.downVoteCount || 0,
    };
  },
};

/**
 * Batch convert multiple API companies to UI format
 */
export const convertApiCompaniesToUI = (apiCompanies: ApiCompany[]): UICompany[] => {
  return apiCompanies.map(companyConverter.apiToUI);
};

/**
 * Batch convert multiple DB companies to UI format
 */
export const convertDBCompaniesToUI = (dbCompanies: DBCompany[]): UICompany[] => {
  return dbCompanies.map(companyConverter.dbToUI);
};

/**
 * Batch convert multiple API companies to DB format
 */
export const convertApiCompaniesToDB = (apiCompanies: ApiCompany[]): DBCompany[] => {
  return apiCompanies.map(companyConverter.apiToDB);
};

/**
 * Validate that a company object has required fields for UI display
 */
export const validateUICompany = (company: any): company is UICompany => {
  return (
    typeof company === 'object' &&
    company !== null &&
    typeof company.companyId === 'number' &&
    typeof company.companyName === 'string' &&
    typeof company.number === 'string'
  );
};

/**
 * Validate that a company object has required fields for database storage
 */
export const validateDBCompany = (company: any): company is DBCompany => {
  return (
    typeof company === 'object' &&
    company !== null &&
    typeof company.company_name === 'string'
  );
};

/**
 * Create a default/empty UI company object
 */
export const createEmptyUICompany = (): UICompany => {
  return {
    companyId: 0,
    companyName: '',
    parentCompany: null,
    companyEmail: null,
    companyLogoUrl: '',
    companyCountry: null,
    companyAddress: null,
    companyWebsite: null,
    upVoteCount: 0,
    downVoteCount: 0,
    createdAt: '',
    number: '',
  };
};

/**
 * Create a default/empty DB company object
 */
export const createEmptyDBCompany = (): DBCompany => {
  return {
    company_id: 0,
    company_name: '',
    parent_company: '',
    company_email: '',
    company_logo_url: '',
    company_country: '',
    company_address: '',
    company_website: '',
    number: '',
    upvote_count: 0,
    downvote_count: 0,
  };
};

export default companyConverter;
